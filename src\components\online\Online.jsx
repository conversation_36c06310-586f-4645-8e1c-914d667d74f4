import "./online.css"
import { Link } from "react-router-dom";

export default function Online({user}) {
    return (
        <li className="rightbarFriend">
            <Link to={`/profile/${user.id}`} style={{ textDecoration: 'none', color: 'inherit' }}>
                <div className="rightbarProfileImgContainer">
                    <img className="rightbarProfileImg" src={user.profilePicture} alt=""/>
                    <span className="rightbarOnline"></span>
                </div>
                <span className="rightbarUsername">{user.username}</span>
            </Link>
        </li>
    );
}