/* General Styling */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(to bottom, #e0eafc, #cfdef3);
    color: #333;
  }
  .like-button {
    background-color: #4a90e2;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    margin: 10px 0;
    transition: background-color 0.3s ease;
  }
  
  .like-button:hover {
    background-color: #357abd;
  }
  
  .comments-section {
    margin-top: 15px;
    border-top: 1px solid #e0e0e0;
    padding-top: 10px;
  }
  
  .comment {
    margin-bottom: 10px;
  }
  
  .comment input {
    width: 100%;
    padding: 8px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  
  .replies {
    margin-left: 15px;
    color: #666;
  }
  
  .reply {
    font-size: 0.9rem;
    color: #555;
  }
  
  /* Navigation Bar */
  .navbar {
    background: linear-gradient(to right, #4a90e2, #50c9c3);
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
  
  .navbar h2 {
    color: #fff;
    margin: 0;
    font-size: 1.8rem;
  }
  
  .navbar ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 20px;
  }
  
  .navbar ul li {
    color: #fff;
    font-size: 1rem;
    cursor: pointer;
  }
  
  .navbar ul li:hover {
    text-decoration: underline;
  }
  
  /* Heading */
  h1 {
    text-align: center;
    margin: 20px 0;
    font-size: 2.5rem;
    color: #333;
  }
  
  /* Post Container */
  .post-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px;
    max-width: 1200px;
    margin: 20px auto;
  }
  
  /* Post Card */
  .post-card {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
  }
  
  .post-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.3);
  }
  
  .post-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }
  
  .post-card h3 {
    margin: 15px;
    font-size: 1.5rem;
    color: #333;
    text-align: center;
    font-weight: bold;
  }
  
  .post-card p {
    margin: 10px 15px;
    font-size: 1rem;
    color: #555;
    text-align: center;
  }
  
  .post-card p strong {
    color: #4a90e2;
  }
  
  /* Footer Section */
.footer {
    background: linear-gradient(to right, #4a90e2, #50c9c3);
    color: #fff;
    padding: 20px 0;
    text-align: center;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
    margin-top: 30px;
  }
  
  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .footer h4 {
    font-size: 1.5rem;
    margin-bottom: 10px;
  }
  
  .footer p {
    margin: 0;
    font-size: 0.9rem;
  }
  
  .footer-links {
    display: flex;
    gap: 15px;
    margin: 15px 0;
  }
  
  .footer-links a {
    color: #fff;
    text-decoration: none;
    font-size: 1rem;
  }
  
  .footer-links a:hover {
    color: #e0eafc;
  }
  
  .footer-icons {
    display: flex;
    gap: 15px;
    margin-top: 10px;
  }
  
  .footer-icons a {
    color: #fff;
    font-size: 1.5rem;
    transition: transform 0.3s ease, color 0.3s ease;
  }
  
  .footer-icons a:hover {
    transform: scale(1.2);
    color: #e0eafc;
  }
  
  .footer small {
    margin-top: 15px;
    display: block;
    font-size: 0.8rem;
    color: #d6e6f3;
  }
  
  
  /* Responsive Design */
  @media (max-width: 768px) {
    h1 {
      font-size: 2rem;
    }
    .post-container {
      padding: 10px;
    }
    .post-card {
      margin: 10px;
    }
  }
  