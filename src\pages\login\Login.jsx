<<<<<<< HEAD
import "./login.css";
import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";

export default function Login() {
  const navigate = useNavigate(); // Hook to navigate programmatically
  const [email, setEmail] = useState(""); // State for email
  const [password, setPassword] = useState(""); // State for password
  const [error, setError] = useState(""); // State for error messages

  const handleLogin = () => {
    // Basic validation logic
    if (!email || !password) {
      setError("Please enter both email and password.");
      return;
    }

    // Simulate authentication (replace with real logic later)
    if (email === "ss" && password === "123") {
      setError(""); // Clear error
      navigate("/"); // Navigate to the Homepage
    } else {
      setError("Invalid email or password.");
    }
  };

  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleLogin();
    }
  };

  // Handle Create Account
  const handleCreateAccount = () => {
    // Navigate to registration page
    navigate("/register");
=======
import React, { useState } from 'react';
import axios from 'axios';
import Cookies from 'js-cookie';
import './login.css';

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    Password: '',
  });

  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post(
        'http://localhost:5000/api/v1/users/login',
        {
          username: formData.username,
          Password: formData.Password,
        },
      );
      const { accessToken, refreshToken, user } = response.data;

      console.log("Access Token:", accessToken);
      console.log("Refresh Token:", refreshToken);
      console.log("User Data:", user);
  

      // Save the access token in a cookie
      Cookies.set('accessToken', accessToken, {
        expires: 1, // 1 day expiration
        secure: false, // Set to true for HTTPS // Helps prevent CSRF attacks
      });

      console.log('Access token saved in cookies:', accessToken);

      setSuccess(true);
      setError(null);
    } catch (err) {
      console.error('Error during login:', err.response?.data || err.message);
      setError(err.response?.data?.message || 'Something went wrong');
      setSuccess(false);
    }
>>>>>>> 431ea09633b8b6986b7bfe2a92c35402b353dc16
  };

  return (
    <div className="login">
      <div className="loginWrapper">
        <div className="loginLeft">
          <h3 className="loginLogo">SocialMediaMP88</h3>
          <span className="loginDesc">Connect with friends and the world around you.</span>
        </div>
        <div className="loginRight">
<<<<<<< HEAD
          <div className="loginBox">
            <input
              placeholder="Email"
              className="loginInput"
              value={email}
              onChange={(e) => setEmail(e.target.value)} // Update email state
              onKeyDown={handleKeyDown} // Handle Enter key
            />
            <input
              type="password"
              placeholder="Password"
              className="loginInput"
              value={password}
              onChange={(e) => setPassword(e.target.value)} // Update password state
              onKeyDown={handleKeyDown} // Handle Enter key
            />
            {error && <span className="loginError">{error}</span>} {/* Display error message */}
            <button className="loginButton" onClick={handleLogin}>
              Log In
            </button>
            <span className="loginForgot">Forgot Password?</span>
            <button className="loginRegisterButton" onClick={handleCreateAccount}>
              Create Account
            </button>
            <div className="loginRegisterLink">
              Don't have an account? <Link to="/register" className="loginRegisterText">Sign Up</Link>
            </div>
          </div>
=======
          <form className="loginBox" onSubmit={handleSubmit}>
            <input
              placeholder="Username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              className="loginInput"
              required
            />
            <input
              placeholder="Password"
              name="Password"
              type="Password"
              value={formData.Password}
              onChange={handleChange}
              className="loginInput"
              required
            />
            <button type="submit" className="loginButton">Log In</button>
            {error && <p className="errorMessage">{error}</p>}
            {success && <p className="successMessage">Login Successful!</p>}
            <span className="loginForgot">Forgot Password?</span>
          </form>
>>>>>>> 431ea09633b8b6986b7bfe2a92c35402b353dc16
        </div>
      </div>
    </div>
  );
<<<<<<< HEAD
}

=======
};

export default Login;
>>>>>>> 431ea09633b8b6986b7bfe2a92c35402b353dc16
