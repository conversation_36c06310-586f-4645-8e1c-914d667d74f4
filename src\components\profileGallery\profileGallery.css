.profileGallery {
    background: white;
    border-radius: 10px;
    box-shadow: 0px 0px 16px -8px rgba(0, 0, 0, 0.2);
    padding: 20px;
    margin-top: 20px;
}

.galleryTitle {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.galleryGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
}

.galleryItem {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.galleryItem:hover {
    transform: scale(1.02);
}

.galleryImg {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.2s ease;
}

.galleryOverlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 15px 10px 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.galleryItem:hover .galleryOverlay {
    opacity: 1;
}

.galleryDesc {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
}

.noPhotosMessage {
    text-align: center;
    padding: 30px;
    color: #666;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    .galleryGrid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 8px;
    }
    
    .profileGallery {
        padding: 15px;
    }
}
