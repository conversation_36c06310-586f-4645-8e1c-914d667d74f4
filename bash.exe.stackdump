Stack trace:
Frame         Function      Args
0007FFFFBE20  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFAD20) msys-2.0.dll+0x1FEBA
0007FFFFBE20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x67F9
0007FFFFBE20  000210046832 (000210285FF9, 0007FFFFBCD8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE20  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBE20  0002100690B4 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFC100  00021006A49D (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF829FA0000 ntdll.dll
7FF829A50000 KERNEL32.DLL
7FF827370000 KERNELBASE.dll
7FF829D90000 USER32.dll
7FF8278B0000 win32u.dll
7FF8292F0000 GDI32.dll
7FF827BD0000 gdi32full.dll
7FF827A60000 msvcp_win.dll
7FF827180000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF829BC0000 advapi32.dll
7FF827E20000 msvcrt.dll
7FF828790000 sechost.dll
7FF8282E0000 RPCRT4.dll
7FF826770000 CRYPTBASE.DLL
7FF8272D0000 bcryptPrimitives.dll
7FF829CD0000 IMM32.DLL
