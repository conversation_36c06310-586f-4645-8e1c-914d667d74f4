
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9f9;
  }
  
  /* Container */
  .create-post-container {
    max-width: 600px;
    margin: 30px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  /* Title */
  .title {
    font-size: 24px;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
  }
  
  /* Form */
  .post-form,
  .comment-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
  }
  
  label {
    font-weight: bold;
    color: #555;
    margin-bottom: 5px;
  }
  
  input[type="text"],
  input[type="file"],
  select,
  textarea {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
  }
  
  textarea {
    resize: none;
  }
  
  /* Buttons */
  .submit-btn {
    padding: 10px 15px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .submit-btn:hover {
    background-color: #0056b3;
  }
  
  /* Comments Section */
  .comments-section {
    margin-top: 30px;
  }
  
  .section-title {
    font-size: 20px;
    color: #444;
    margin-bottom: 15px;
  }
  
  .comment-form input {
    flex: 1;
    margin-bottom: 10px;
  }
  
  .comment-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .comment-item {
    background-color: #f1f1f1;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #333;
  }
  