.share{
    width: 100%;
    height: 120px;
    border-radius: 10px;
    background-color: rgb(245, 238, 238);
    -webkit-box-shadow: 0px 0px 16px -8px black;
    box-shadow: 0px 0px 16px -8px black;
}
.shareWrapper{
    padding: 10px;
}
.shareTop{
    display: flex;
    align-items: center;
}
.shareProfileImg{
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
}.shareInput{
    border: none;
    width: 80%;
}
.shareInput:focus{
    outline: none;
}
.shareHr{
    margin: 20px;
}
.shareBottom{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 20px;
}
.shareButton{
    border: none;
    padding: 7px;
    border-radius: 5px;
    background-color: rgb(84, 202, 139);
    font-weight: 500;
    cursor: pointer;
    color: white;
}